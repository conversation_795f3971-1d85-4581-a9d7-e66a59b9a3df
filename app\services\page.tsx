"use client";
import Image from "next/image";
import { useState } from "react";

// Strongly-typed categories
type CategoryKey = "car" | "rv" | "boat";

interface ServiceDetail {
    title: string;
    description: string;
    image: string;
    category: CategoryKey;
}

const servicesDetails: ServiceDetail[] = [
    // Car Services
    {
        title: "Interior Detailing",
        description:
            "This is our bread and butter. From vacuuming carpets to cleaning leather, we make every inch of your vehicle’s interior look and feel like new. Our service ensures a refreshed look and a healthier space for you and your passengers. Ask about our recurring detailing plans!",
        image: "/interior detailing 1.png",
        category: "car",
    },
    {
        title: "Exterior Detailing",
        description:
            "Complete exterior care. Our Exterior Detailing includes a professional wash, light wax, and polish to protect and enhance your vehicle’s finish.",
        image: "/exterior wash 1.jpg",
        category: "car",
    },
    {
        title: "Interior & Exterior Detailing",
        description:
            "Full-service Interior & Exterior Detailing combining advanced cleaning inside and out for a complete refresh.",
        image: "/interior detailing 3.jpg",
        category: "car",
    },
    {
        title: "Paint Correction/Polishing",
        description:
            "Restore your car’s original shine by removing swirls, scratches, and imperfections with our expert polishing and paint correction.",
        image: "/polishing 1.jpg",
        category: "car",
    },
    {
        title: "Ceramic Coating",
        description:
            "Shield your car’s paint with advanced ceramic coating for unmatched protection and enduring gloss.",
        image: "/ceramic2.jpg",
        category: "car",
    },
    // RV Services
    {
        title: "RV Exterior Wash, Spray Wax & Towel Dry",
        description:
            "Complete exterior revival for your RV: power wash, spray wax, and hand towel dry to keep it looking showroom-new.",
        image: "/rv1.jpg",
        category: "rv",
    },
    {
        title: "Roof Cleaning",
        description:
            "Comprehensive roof cleaning for RVs to remove debris, mold, and mildew, extending the life of your roof sealant.",
        image: "/rvroof1.jpg",
        category: "rv",
    },
    {
        title: "One Step Deep Polish / Oxidation Removal",
        description:
            "Eliminate oxidation and restore gloss with our one-step deep polish, perfect for RV fiberglass exteriors.",
        image: "/rv polishing.png",
        category: "rv",
    },
    {
        title: "RV Ceramic Coating",
        description:
            "Long-lasting ceramic protection to guard your RV against UV rays, road salts, and environmental contaminants.",
        image: "/RV ceramic.png",
        category: "rv",
    },
    // Boat Services
    {
        title: "Vacuum & Wipedown / Pressure Wash Interior",
        description:
            "Deep-clean every corner of your boat’s interior with vacuuming, wipedown, and pressure wash for a refreshed cabin.",
        image: "/boatinterior1.jpg",
        category: "boat",
    },
    {
        title: "Boat Exterior Wash, Light Algae Removal, Spray Wax & Towel Dry",
        description:
            "Protect and shine your boat’s hull with algae removal, spray wax application, and hand towel drying.",
        image: "/boat washing.png",
        category: "boat",
    },
    {
        title: "1 Step Deep Polish / Oxidation Removal / Heavy Algae Removal",
        description:
            "Aggressive polish and oxidation removal combined with heavy algae cleaning for a mirror-like finish.",
        image: "/boatpolishing.jpg",
        category: "boat",
    },
    {
        title: "Boat Ceramic Coating",
        description:
            "Ultimate marine-grade ceramic coating to protect against saltwater, UV, and oxidation for lasting gloss.",
        image: "/boat ceramic.png",
        category: "boat",
    },
];

const categoryOrder: CategoryKey[] = ["car", "rv", "boat"];

const ServicesPage = () => {
    const [showBookingModal, setShowBookingModal] = useState(false);

    return (
        <main
            id="main-content"
            className="pt-20 flex flex-col items-center min-h-screen space-y-8 p-4 sm:p-8 font-sans"
        >
            {showBookingModal && (
                <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
                    <div className="bg-white rounded-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
                        <div className="p-4 flex justify-between items-center border-b">
                            <h2 className="text-xl font-bold text-gray-800">Booking Details</h2>
                            <button
                                onClick={() => setShowBookingModal(false)}
                                className="text-gray-500 hover:text-gray-700"
                            >
                                ✕
                            </button>
                        </div>
                    </div>
                </div>
            )}

            <section className="w-full max-w-6xl mx-auto px-2 text-center relative">
                <h1 className="mb-2 text-6xl md:text-6xl font-bold text-white tracking-tight">
                    Our Services
                </h1>
                <p className="text-lg text-white/90 max-w-3xl mx-auto mb-8">
                    We look forward to serving you with our expert mobile detailing for cars, RVs, and boats.
                </p>
                {/* Three columns: specific category grouping */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                    {categoryOrder.map((cat) => (
                        <div
                            key={cat}
                            id={cat}
                            className={`
                                space-y-6 p-4 rounded-xl scroll-mt-20
                                ${cat === "car" ? "bg-slate-900/30" : ""}
                                ${cat === "rv" ? "bg-slate-900/30" : ""}
                                ${cat === "boat" ? "bg-slate-900/30" : ""}
                            `}
                        >
                            {servicesDetails
                                .filter((s) => s.category === cat)
                                .map((service) => (
                                    <div
                                        key={service.title}
                                        className="relative bg-slate-900/80 backdrop-blur-sm rounded-lg border border-white/30 p-6 flex flex-col items-center space-y-4 hover:shadow-lg hover:shadow-pink-500/20 transition-shadow"
                                    >
                                        <div className="relative w-full h-48 overflow-hidden rounded-md shadow-sm">
                                            <Image
                                                src={encodeURI(service.image)}
                                                alt={`${service.title} Service`}
                                                fill
                                                className="object-cover"
                                            />
                                        </div>
                                        <h4 className="text-xl font-semibold text-white text-center">
                                            {service.title}
                                        </h4>
                                        <p className="text-sm text-white/90 text-center">{service.description}</p>
                                        <button
                                            onClick={() => setShowBookingModal(true)}
                                            className="group relative bg-pink-500 text-white px-4 py-2 rounded-lg font-semibold hover:bg-pink-600 transition-all transform hover:scale-105 hover:shadow-lg hover:shadow-pink-500 border border-white"
                                        >
                                            <span className="relative z-10 text-sm">
                                                Learn More & Book Now
                                            </span>
                                            <span className="absolute inset-0 bg-gradient-to-r from-pink-500 to-pink-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></span>
                                        </button>
                                    </div>
                                ))}
                        </div>
                    ))}
                </div>
            </section>
        </main>
    );
};

export default ServicesPage;